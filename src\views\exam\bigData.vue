<template>
  <div class="big-data" @click="onPageClick">
    <!-- 顶部导航-->
    <div
      class="top-nav"
      :class="{ white_bg: topShow }"
      :style="{ 'padding-top': topHeight + 'rem', height: 1.17 + topHeight + 'rem' }"
    >
      <img
        @click="backPage"
        v-if="!topShow"
        class="img"
        src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/back_white.png"
        alt=""
      />
      <img
        @click="backPage"
        v-else
        class="img"
        src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/back_black.png"
        alt=""
      />
      <div v-if="topShow" class="title">报考大数据</div>
      <img
        @click="sharePage"
        v-if="!topShow"
        class="img"
        src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/share_white.png"
        alt=""
      />
      <img
        @click="sharePage"
        v-else
        class="img"
        src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/share_black.png"
        alt=""
      />
    </div>

    <!-- 顶部背景区域 -->
    <div class="top-bg" :style="{ height: isInAPP ? '9.33rem' : '8.27rem' }">
      <img
        class="bg-img"
        :src="`https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/${isInAPP ? 'app' : 'big'}_data_top_bg.png`"
      />
      <div class="top-area" :style="{ 'padding-top': 0.21 + topHeight + 'rem' }">
        <img
          class="top-title-img"
          src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/big_data_top_title.png"
        />
        <div class="title">最后更新时间：{{ updateTime }}</div>

        <!-- 选择器区域 -->
        <!-- 平板布局：三个选择器在同一行 -->
        <div v-if="isTablet" class="select-tablet">
          <div class="select-item" @click="onExamTypeClick">
            <div class="text">{{ selectedExamTypeText }}</div>
            <img
              class="arrow"
              src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/big_data_top_select.png"
            />
          </div>
          <div class="select-item" @click="onRegionClick">
            <div class="text">{{ selectedRegionText }}</div>
            <img
              class="arrow"
              src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/big_data_top_select.png"
            />
          </div>
          <div class="select-item" @click="onSpecificExamClick">
            <div class="text">{{ projectName }}</div>
            <img
              class="arrow"
              src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/big_data_top_select.png"
            />
          </div>
        </div>

        <!-- 手机布局：原有的2+1布局 -->
        <template v-else>
          <div class="select-one">
            <div class="select-item" @click="onExamTypeClick">
              <div class="text">{{ selectedExamTypeText }}</div>
              <img
                class="arrow"
                src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/big_data_top_select.png"
              />
            </div>
            <div class="select-item" @click="onRegionClick">
              <div class="text">{{ selectedRegionText }}</div>
              <img
                class="arrow"
                src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/big_data_top_select.png"
              />
            </div>
          </div>

          <div class="select-item w100" @click="onSpecificExamClick">
            <div class="text">{{ projectName }}</div>
            <img
              class="arrow"
              src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/big_data_top_select.png"
            />
          </div>
        </template>
      </div>
    </div>
    <!-- Loading 状态 - 只在首次初始化时显示 -->
    <div v-if="isInitialLoading" class="loading-overlay">
      <div class="loading-spinner">
        <div class="spinner" :style="{ 'border-top': '0.1rem solid ' + brandColor }"></div>
      </div>
    </div>

    <!-- 主内容区域 -->
    <div
      class="main-content"
      v-if="detailData.title"
      :style="{ 'margin-top': getMainContentMarginTop() }"
    >
      <!-- Tab导航区域 -->
      <div
        v-if="tabList.length > 1"
        ref="tabContainerRef"
        class="tab-container"
        :class="{ 'no-radius': isTabSticky }"
        :style="{ top: 1.17 + topHeight + 'rem' }"
      >
        <div class="tab-scroll">
          <div class="tab-list">
            <div
              v-for="(tab, index) in tabList"
              :key="index"
              class="tab-item"
              :class="{ active: currentTab === index }"
              @click="onTabClick(index)"
            >
              {{ tab.name }}
            </div>
          </div>
        </div>
      </div>

      <div
        v-if="detailData.title"
        ref="contentContainerRef"
        style="background: #fff"
        :class="{ 'no-tab-list': tabList.length < 2 }"
      >
        <!-- 内容区域 -->
        <div class="content-container">
          <div class="content-area">
            <!-- 招聘公告标题 -->
            <div
              v-if="isInAPP && detailData.title"
              class="announcement-title"
              @click="goAppUrl('notice')"
            >
              <span class="text">{{ detailData.title }}</span>
              <img
                class="arrow"
                src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/announcement_title_arrow.png"
                alt=""
              />
            </div>
            <open-app
              class="announcement-title"
              v-if="!isInAPP && detailData.title"
              :options="optionsParam"
            >
              <span class="text">{{ detailData.title }}</span>
              <img
                class="arrow"
                src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/announcement_title_arrow.png"
                alt=""
              />
            </open-app>
            <!-- 数据统计卡片 -->
            <div
              class="stats-container"
              v-if="detailData.sector_list && detailData.sector_list.length"
            >
              <!-- 报名总人数 -->
              <!--            <div class="stats-card large-card orange-card">-->
              <!--              <div class="card-title">报名总人数</div>-->
              <!--              <div class="card-number">-->
              <!--                <span class="number">{{ detailData.apply_num_info?.total_apply_num || "0" }}</span>-->
              <!--                <span class="unit">人</span>-->
              <!--              </div>-->
              <!--            </div>-->

              <!-- 报名人数较多的单位 -->
              <div
                v-for="(item, index) in detailData.sector_list"
                :key="index"
                class="stats-card large-card"
                :class="getCardClass(index)"
                @click="onStatsCardClick(item)"
              >
                <div class="card-title">
                  {{ item.title }}
                  <img
                    v-if="item.type != 'normal'"
                    class="arrow-icon"
                    src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/arrow_right_1.png"
                  />
                </div>
                <div class="card-number">
                  <span class="number" v-if="item.type == 'competitive_rate'">
                    {{ item.value || "-" }}
                  </span>
                  <span class="number" v-else>
                    {{ item.value && item.value > 0 ? item.value : "-" }}
                  </span>
                  <span class="unit" v-if="item.value && item.value > 0">{{ item.unit }}</span>
                  <template v-if="item.type == 'competitive_rate'">
                    <div class="card-type" v-if="item.tag && item.value">
                      {{ item.tag }}
                    </div>
                  </template>
                  <template v-else>
                    <div class="card-type" v-if="item.tag && item.value && item.value > 0">
                      {{ item.tag }}
                    </div>
                  </template>
                </div>
                <template v-if="item.type == 'competitive_rate'">
                  <div class="card-desc" v-if="item.desc && item.value">
                    {{ item.desc }}
                  </div>
                </template>
                <template v-else>
                  <div class="card-desc" v-if="item.desc && item.value && item.value > 0">
                    {{ item.desc }}
                  </div>
                </template>
              </div>

              <!-- 竞争激烈职位 -->
              <!--            <div class="stats-card large-card light-blue-card">-->
              <!--              <div class="card-title">-->
              <!--                竞争激烈职位-->
              <!--                <img-->
              <!--                  class="arrow-icon"-->
              <!--                  src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/arrow_right_2.png"-->
              <!--                />-->
              <!--              </div>-->
              <!--              <div class="card-number">-->
              <!--                <span class="number">-->
              <!--                  {{ detailData.apply_num_info?.max_competitive_rate || "0:1" }}-->
              <!--                </span>-->
              <!--                <div class="card-type">最高竞争比</div>-->
              <!--              </div>-->
              <!--              <div class="card-desc">-->
              <!--                {{ detailData.apply_num_info?.avg_competitive_rate_text || "" }}-->
              <!--              </div>-->
              <!--            </div>-->

              <!-- 报名人数较少职位 -->
              <!--            <div class="stats-card large-card cyan-card">-->
              <!--              <div class="card-title">-->
              <!--                报名人数较少职位-->
              <!--                <img-->
              <!--                  class="arrow-icon"-->
              <!--                  src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/arrow_right_3.png"-->
              <!--                />-->
              <!--              </div>-->
              <!--              <div class="card-number">-->
              <!--                <span class="number">-->
              <!--                  {{ detailData.apply_num_info?.zero_apply_num_job || "0" }}-->
              <!--                </span>-->
              <!--                <span class="unit">个</span>-->
              <!--                <div class="card-type">暂无人报名</div>-->
              <!--              </div>-->
              <!--              <div class="card-desc">-->
              <!--                {{ detailData.apply_num_info?.low_competitive_rate_text || "" }}-->
              <!--              </div>-->
              <!--            </div>-->
            </div>

            <!-- 数据说明 -->
            <div class="data-desc">
              <div class="data-desc-content" :class="{ collapsed: !dataDescExpanded }">
                <span class="data-desc-text">
                  数据说明：
                  {{ detailData.desc }}
                  <span v-if="dataDescExpanded" class="expand-btn inline" @click="toggleDataDesc">
                    <span class="blue-text">收起</span>
                    <img
                      src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/arrow_down.png"
                      class="arrow-icon rotated"
                      alt="收起"
                    />
                  </span>
                </span>
                <span v-if="!dataDescExpanded" class="expand-btn" @click="toggleDataDesc">
                  <span class="blue-text">展开</span>
                  <img
                    src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/arrow_down.png"
                    class="arrow-icon"
                    alt="展开"
                  />
                </span>
              </div>
            </div>
          </div>
        </div>

        <!--  占位符-->
        <div class="zhanwei-box"></div>

        <!-- 图表区域 -->
        <BigDataChart
          v-if="detailData.region_num_list && detailData.region_num_list.length > 1"
          title="各地域报名数据"
          :chartData="detailData.region_num_list"
          nameKey="area_name"
          dataKey="num"
          :tooltipFormatter="customTooltipFormatter"
        />
        <!--  占位符-->
        <div
          class="zhanwei-box"
          v-if="detailData.region_num_list && detailData.region_num_list.length > 1"
        ></div>
        <!-- 职位数据表格 -->
        <div class="position-section" ref="positionSectionRef" v-if="currentTableData.length">
          <div class="position-header">
            <div class="position-title">职位大数据</div>
            <div
              v-if="detailData.region_num_list && detailData.region_num_list.length > 1"
              class="position-location"
              @click="onPositionLocationClick"
            >
              {{ regionName }}
              <img
                class="location-arrow"
                src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/big_data_gray_select.png"
              />
            </div>
          </div>
          <div class="position-tab-container">
            <div class="position-tab-list">
              <div
                class="position-tab-item"
                :class="{ active: currentMainTab === 'hot' }"
                @click="onMainTabClick('hot')"
              >
                热门
              </div>
              <div
                class="position-tab-item"
                :class="{ active: currentMainTab === 'cold' }"
                @click="onMainTabClick('cold')"
              >
                冷门
              </div>
            </div>
            <div class="position-sub-tab-list">
              <div
                class="position-sub-tab-item"
                :class="{ active: selectedSubTab === 'num' }"
                @click="onSubTabClick('num')"
              >
                按{{ getMainTypeText(detailData.show_filed) }}数
              </div>
              <div
                class="position-sub-tab-item"
                :class="{ active: selectedSubTab === 'competitive_rate' }"
                @click="onSubTabClick('competitive_rate')"
              >
                按竞争比
              </div>
            </div>
          </div>

          <!-- 表格内容 -->
          <div class="position-table-content">
            <!-- 表格头部 -->
            <div class="position-table-header">
              <div class="header-item header-rank"></div>
              <div class="header-item header-position">
                <span>职位</span>
                <span>名称</span>
              </div>
              <div class="header-item header-unit">
                <span>招考</span>
                <span>单位</span>
              </div>
              <div class="header-item header-recruit">
                <span>招录</span>
                <span>人数</span>
              </div>
              <div class="header-item header-apply" v-if="selectedSubTab === 'num'">
                <span>{{ getMainTypeText(detailData.show_filed) }}</span>
                <span>人数</span>
              </div>
              <div class="header-item header-apply" v-else>
                <span>竞争</span>
                <span>比</span>
              </div>
            </div>

            <!-- 表格数据 -->
            <div class="position-table-body">
              <div
                v-for="(item, index) in currentTableData"
                :key="index"
                class="position-table-row"
              >
                <div class="table-item table-rank">
                  <div class="rank-badge" :class="`rank-${index + 1}`">{{ index + 1 }}</div>
                </div>
                <div class="table-item table-position">
                  <span class="position-title" @click="goJobDetail(item.id)">{{ item.name }}</span>
                </div>
                <div class="table-item table-unit">
                  <span class="unit-title">{{ item.work_unit }}</span>
                </div>
                <div class="table-item table-recruit">
                  <span class="recruit-num">{{ item.need_num }}</span>
                </div>
                <div class="table-item table-apply">
                  <span class="apply-num" v-if="selectedSubTab === 'num'">{{ item.num }}</span>
                  <span class="apply-num" v-else>{{ item.competitive_rate }}</span>
                </div>
              </div>
            </div>

            <!-- 查看更多 -->
            <div v-if="showLoadMore" class="load-more-section" @click="onLoadMoreClick">
              <div class="load-more-text">查看更多</div>
              <img
                class="load-more-icon"
                src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/big_data_gray_select.png"
              />
            </div>
          </div>
        </div>

        <!--  占位符-->
        <div class="zhanwei-box" v-if="currentTableData.length"></div>

        <!-- 招考单位TOP10 -->
        <div
          class="recruiting-unit"
          ref="recruitingUnitRef"
          v-if="detailData.work_unit_list.length"
        >
          <div class="recruit-title">招考单位 TOP10</div>
          <div class="position-table-content-2">
            <!-- 表格头部 -->
            <div class="position-table-header">
              <div class="header-item header-rank"></div>
              <div class="header-item header-position">
                <span>招考</span>
                <span>单位</span>
              </div>
              <div class="header-item header-unit">
                <span>职位</span>
                <span>个数</span>
              </div>
              <div class="header-item header-recruit">
                <span>招录</span>
                <span>人数</span>
              </div>
              <div class="header-item header-apply">
                <span>{{ getMainTypeText(detailData.show_filed) }}</span>
                <span>人数</span>
              </div>
              <div class="header-item header-competition">
                <span>竞争</span>
                <span>比例</span>
              </div>
            </div>

            <!-- 表格数据 -->
            <div class="position-table-body">
              <div
                v-for="(item, index) in detailData.work_unit_list"
                :key="index"
                class="position-table-row"
              >
                <div class="table-item table-rank">
                  <div class="rank-badge" :class="`rank-${index + 1}`">{{ index + 1 }}</div>
                </div>
                <div class="table-item table-position">
                  <span class="position-title">{{ item.work_unit }}</span>
                </div>
                <div class="table-item table-unit">
                  <span class="unit-title">{{ item.job_num }}</span>
                </div>
                <div class="table-item table-recruit">
                  <span class="recruit-num">{{ item.need_num }}</span>
                </div>
                <div class="table-item table-apply">
                  <span class="apply-num">{{ item.num }}</span>
                </div>
                <div class="table-item table-competition">
                  <span class="competition-rate">{{ item.competitive_rate }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 宣传横幅图片 -->
          <img
            v-if="detailData.bottom_ad && detailData.bottom_ad.length"
            src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/zhaokao-bg.png"
            class="promo-banner-img"
            alt="2025国考公告解读峰会"
          />
        </div>

        <!--  占位符-->
        <div class="zhanwei-box" v-if="detailData.work_unit_list.length"></div>

        <!-- 岗位报考咨询区域 -->
        <div ref="jobSearchSectionRef">
          <JobSearchForm
            title="职位报考咨询"
            :regionColumns="positionRegionColumns"
            :regionDisplayText="positionRegionSelector.displayText"
            :unitOptions="searchUnitOptions"
            :selectedUnitText="selectedSearchUnitText"
            :positionInput="positionInput"
            @regionClick="onPositionRegionClick"
            @regionConfirm="onSearchRegionConfirm"
            @unitClick="showSearchUnitPopup = true"
            @unitConfirm="onSearchUnitConfirm"
            @search="onSearchClick"
            @clear="onClearSearch"
            @positionInputChange="(value) => (positionInput = value)"
          />
        </div>

        <!-- 结果统计区域 -->
        <div class="result-area" v-if="jobData.count > 0">
          <div class="result-header">
            <div class="result-left">
              <span class="result-text">
                共
                <span class="result-number">{{ jobData.count || 0 }}</span>
                个职位
              </span>
              <div class="sort-btn" @click="changeSortOrder">
                <span>竞争比</span>
                <img
                  :src="`https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/${sortOrder === 'asc' ? 'arrow_top' : sortOrder === 'desc' ? 'arrow_bottom' : 'arrow_all'}.png`"
                  class="sort-icon"
                />
              </div>
            </div>
            <div v-if="isInAPP" class="result-right" @click="goAppUrl('focus')">
              <span class="focus-text">我的关注</span>
              <img
                src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/arrow_right_blue.png"
                class="arrow-icon"
              />
            </div>
          </div>

          <!-- 职位卡片列表 -->
          <div class="job-card-list">
            <div
              v-for="(job, index) in jobData.list"
              :key="index"
              class="job-card"
              @click="openJob(job)"
            >
              <template v-if="isInAPP">
                <div class="job-card-top">
                  <div class="job-name">{{ job.name }}</div>
                  <div
                    class="focus-btn"
                    :class="{ focused: job.is_follows == 1 }"
                    @click.stop="toggleFocus(index)"
                  >
                    <img
                      :src="
                        job.is_follows == 1
                          ? 'https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/focused.png'
                          : 'https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_add_focus_red.png'
                      "
                      class="focus-icon"
                    />
                    <span class="focus-text">{{ job.is_follows == 1 ? "已关注" : "关注" }}</span>
                  </div>
                </div>
                <div class="job-data-area">
                  <div class="data-item" v-if="job.work_unit">
                    <span class="data-title">招录地区：</span>
                    <span class="data-content">{{ job.work_unit }}</span>
                  </div>
                  <div class="data-item" v-if="job.need_num && job.need_num > 0">
                    <span class="data-title">招录人数：</span>
                    <span class="data-content">
                      <span class="bold">{{ job.need_num }}</span>
                      人
                    </span>
                  </div>
                  <div class="data-item" v-if="job.apply_num && job.apply_num > 0">
                    <span class="data-title">报考人数：</span>
                    <span class="data-content">
                      <span class="bold">{{ job.apply_num }}</span>
                      人
                    </span>
                  </div>
                  <div class="data-item" v-if="job.approved_num && job.approved_num > 0">
                    <span class="data-title">过审人数：</span>
                    <span class="data-content">
                      <span class="bold">{{ job.approved_num }}</span>
                      人
                    </span>
                  </div>
                  <div class="data-item" v-if="job.pay_num && job.pay_num > 0">
                    <span class="data-title">缴费人数：</span>
                    <span class="data-content">
                      <span class="bold">{{ job.pay_num }}</span>
                      人
                    </span>
                  </div>
                  <div class="data-item mb0" v-if="job.competitive_rate">
                    <span class="data-title">竞争比：</span>
                    <span class="data-content red">
                      <span class="bold">{{ job.competitive_rate || "-" }}</span>
                    </span>
                  </div>
                </div>
              </template>
              <template v-else>
                <open-app :options="optionsParam">
                  <div class="job-card-top">
                    <div class="job-name">{{ job.name }}</div>
                    <div
                      v-if="isInAPP"
                      class="focus-btn"
                      :class="{ focused: job.is_follows == 1 }"
                      @click="toggleFocus(index)"
                    >
                      <img
                        :src="
                          job.is_follows == 1
                            ? 'https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/focused.png'
                            : 'https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/common/job_add_focus_red.png'
                        "
                        class="focus-icon"
                      />
                      <span class="focus-text">{{ job.is_follows == 1 ? "已关注" : "关注" }}</span>
                    </div>
                  </div>
                  <div class="job-data-area">
                    <div class="data-item" v-if="job.work_unit">
                      <span class="data-title">招录地区：</span>
                      <span class="data-content">{{ job.work_unit }}</span>
                    </div>
                    <div class="data-item" v-if="job.need_num && job.need_num > 0">
                      <span class="data-title">招录人数：</span>
                      <span class="data-content">
                        <span class="bold">{{ job.need_num }}</span>
                        人
                      </span>
                    </div>
                    <div class="data-item" v-if="job.apply_num && job.apply_num > 0">
                      <span class="data-title">报考人数：</span>
                      <span class="data-content">
                        <span class="bold">{{ job.apply_num }}</span>
                        人
                      </span>
                    </div>
                    <div class="data-item" v-if="job.approved_num && job.approved_num > 0">
                      <span class="data-title">过审人数：</span>
                      <span class="data-content">
                        <span class="bold">{{ job.approved_num }}</span>
                        人
                      </span>
                    </div>
                    <div class="data-item" v-if="job.pay_num && job.pay_num > 0">
                      <span class="data-title">缴费人数：</span>
                      <span class="data-content">
                        <span class="bold">{{ job.pay_num }}</span>
                        人
                      </span>
                    </div>
                    <div class="data-item mb0" v-if="job.competitive_rate">
                      <span class="data-title">竞争比：</span>
                      <span class="data-content red">
                        <span class="bold">{{ job.competitive_rate || "-" }}</span>
                      </span>
                    </div>
                  </div>
                </open-app>
              </template>
            </div>
          </div>
        </div>
      </div>
      <!--      <EmptyDefault :isNewStyle="true" v-else text="暂无内容" />-->
    </div>

    <!-- 底部固定导航栏 -->
    <!--    <div v-if="detailData.title" class="action-bar-box">-->
    <!--      <div v-if="isInAPP" class="bottom-fixed-nav">-->
    <!--        <div class="nav-left" v-if="detailData.job_selection.icon">-->
    <!--          <div class="nav-item" @click="goCmdUrl">-->
    <!--            <img :src="detailData.job_selection.icon" class="nav-icon" />-->
    <!--            <span>{{ detailData.job_selection.title }}</span>-->
    <!--          </div>-->
    <!--          <div class="nav-item" @click="goAppUrl('notice')">-->
    <!--            <img-->
    <!--              src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/news.png"-->
    <!--              class="nav-icon"-->
    <!--            />-->
    <!--            <span>查看公告</span>-->
    <!--          </div>-->
    <!--        </div>-->
    <!--        <div class="nav-item-area">-->
    <!--          <div class="nav-btn" @click="turnToPage">岗位报考查询</div>-->
    <!--        </div>-->
    <!--      </div>-->
    <!--      <div v-else class="bottom-fixed-nav">-->
    <!--        <div class="nav-left" v-if="detailData.job_selection.icon">-->
    <!--          <open-app class="nav-item" :options="optionsParam">-->
    <!--            <img :src="detailData.job_selection.icon" class="nav-icon" />-->
    <!--            <span>{{ detailData.job_selection.title }}</span>-->
    <!--          </open-app>-->
    <!--        </div>-->
    <!--        <open-app class="nav-item-area" :options="optionsParam">-->
    <!--          <div class="nav-btn">查看公告</div>-->
    <!--        </open-app>-->
    <!--      </div>-->
    <!--    </div>-->

    <!-- Vant选择器弹窗 -->
    <van-popup v-model:show="examTypeVisible" position="bottom">
      <van-picker
        v-model="selectedExamValues"
        :columns="examTypeOptions"
        @confirm="onExamTypeConfirm"
        @cancel="examTypeVisible = false"
      />
    </van-popup>

    <van-popup v-model:show="regionVisible" position="bottom">
      <van-picker
        v-model="selectedRegionValues"
        title="请选择所属地区"
        :columns="regionOptions"
        @confirm="onRegionConfirm"
        @cancel="regionVisible = false"
      />
    </van-popup>

    <van-popup v-model:show="specificExamVisible" position="bottom">
      <van-picker
        :columns="specificExamOptions"
        @confirm="onSpecificExamConfirm"
        @cancel="specificExamVisible = false"
      />
    </van-popup>

    <!-- 岗位报考查询相关弹窗 -->
    <van-popup
      v-model:show="showSearchRegionPopup"
      position="bottom"
      @open="onPositionRegionPopupOpen"
    >
      <van-picker
        ref="positionRegionPickerRef"
        :columns="positionRegionColumns"
        @confirm="onPositionRegionConfirm"
        @cancel="showSearchRegionPopup = false"
        @change="onPositionRegionPickerChange"
        :title="positionRegionSelector.title"
      />
    </van-popup>

    <van-popup v-model:show="showSearchUnitPopup" position="bottom">
      <van-picker
        :columns="searchUnitOptions"
        @confirm="onSearchUnitConfirm"
        @cancel="showSearchUnitPopup = false"
      />
    </van-popup>

    <van-popup v-model:show="positionLocationVisible" position="bottom">
      <van-picker
        :columns="positionLocationOptions"
        @confirm="onPositionLocationConfirm"
        @cancel="positionLocationVisible = false"
      />
    </van-popup>
  </div>
  <!-- 悬浮咨询框 -->
  <FloatingBox :options="optionsParam" v-if="!isInAPP" />
</template>

<script setup>
import { computed, nextTick, onBeforeMount, onMounted, onUnmounted, reactive, ref } from "vue"
import { useRoute, useRouter } from "vue-router"
import {
  getApplyDetailRequest,
  getArticleListRequest,
  getConfigRequest,
  getJobCompetitiveRateListRequest,
  getOverViewRequest,
  getProjectListRequest,
  getRegionListByArticleIdRequest,
  getRegionTreeRequest,
  getStatisticsListRequest,
  getWorkUnitListRequest,
  setFocus,
} from "@/api/bigData"
import { isDeviceAppClientCookie } from "@/utils/cookies/modules/appDevice"
import FloatingBox from "@/components/SideOpenApp.vue"
import OpenApp from "@/components/OpenApp.vue"
import BigDataChart from "./components/BigDataChart.vue"
import JobSearchForm from "./components/JobSearchForm.vue"
import {
  closeAppWebview,
  getAppStatusHeight,
  openAppFullscreen,
  openAppSharePopup,
  openUrlAction,
  setAppMeta,
} from "@/utils/appDevice.js"
import { showToast } from "vant"
import "vant/es/toast/style"
import { setShare } from "@/utils/share.js"
import { useBrandStore } from "@/stores/brand.js"

const router = useRouter()
const route = useRoute()
const positionRegionPickerRef = ref(null)
const jobSearchSectionRef = ref(null)
const positionSectionRef = ref(null)
const recruitingUnitRef = ref(null)
const tabContainerRef = ref(null)
const contentContainerRef = ref(null)

// 组件声明
defineOptions({
  components: {
    FloatingBox,
  },
})

// 从路由中获取参数
const routeProjectId = computed(() => route.query.project_id || route.params.project_id)
const routeArticleId = computed(() => route.query.article_id || route.params.article_id)

const getCardClass = (index) => {
  let str = "orange-card"
  switch (index) {
    case 1:
      str = "blue-card"
      break
    case 2:
      str = "light-blue-card"
      break
    case 3:
      str = "cyan-card"
      break
  }
  return str
}

const getMainTypeText = (type) => {
  if (type == "pay_num") {
    return "缴费"
  }
  if (type == "approved_num") {
    return "过审"
  }
  return "报名"
}

// 响应式数据
const updateTime = ref("")

// Loading 状态管理
const isInitialLoading = ref(true) // 首次加载时的loading状态

// 滚动位置记忆
const scrollPositions = ref({}) // 保存每个tab的滚动位置

// 平板适配相关
const isTablet = ref(false)
const stickyThreshold = ref(0)
const isTabSticky = ref(false)

// 选择器相关数据
const examTypeVisible = ref(false)
const examTypeOptions = ref([])
const selectedExamType = ref("")
const selectedExamTypeKey = ref("")
const selectedExamValues = ref([])

const regionVisible = ref(false)
const regionOptions = ref([])
const selectedRegion = ref("")
const selectedRegionKey = ref("")
const selectedRegionValues = ref([])

const specificExamVisible = ref(false)
const specificExamOptions = ref([])
const selectedSpecificExam = ref("")
const projectName = ref("")

// 全局配置数据
const globalConfig = ref(null)

// 项目和文章数据
const projectList = ref([])
const articleList = ref([])
const projectId = ref(null)
const articleId = ref(null)

// Tab相关数据
const tabList = ref([])
const currentTab = ref(0)

// 表格相关数据
const currentMainTab = ref("hot")
const selectedSubTab = ref("num")
const currentTableData = ref([])
const showLoadMore = ref(true)
const displayCount = ref(10)

// 职位数据
const jobData = reactive({
  count: 0,
  list: [],
})

// 分页参数
const pageParams = reactive({
  page: 1,
  size: 20,
  hasMore: true,
  loading: false,
})

// 岗位报考咨询相关数据
const showSearchRegionPopup = ref(false)
const showSearchUnitPopup = ref(false)
const selectedSearchRegionText = ref("全部地区")
const selectedSearchUnitText = ref("全部单位")
const selectedSearchUnitKey = ref("")
const positionInput = ref("")
const sortOrder = ref("") // 'asc', 'desc', ''
const sortIndex = ref(0)

// 搜索相关数据
const hasSearched = ref(false)

// 数据说明
const dataDescExpanded = ref(false)

// 地区相关数据
const regionId = ref("")
const regionName = ref("")

const searchRegionOptions = ref([{ text: "全部地区", value: "" }])

const searchUnitOptions = ref([{ text: "全部单位", value: "" }])

// 用人单位列表
const workList = ref([])

// 详情数据
const detailData = ref({
  title: "",
  apply_num_info: {},
  work_unit_list: [],
  region_apply_num_list: [],
})

// 地区数据缓存
const regionDataCache = ref({})

// 地区选择器相关
const positionRegionSelector = reactive({
  level: 1, // 1=三级联动, 2=市区二级, 3=只选区, 4=不可编辑
  editable: true,
  title: "请选择地区",
  selectedCodes: [],
  selectedValues: [], // [省id, 市id, 区id]
  displayText: "全部地区",
})

// 地区数据
const areaList = ref({})
const positionRegionColumns = ref([])

const cascaderValue = ref("")

const positionLocationVisible = ref(false)
const positionLocationOptions = computed(() => {
  return (
    detailData.value.region_num_list?.map((item) => ({
      text: item.area_name,
      value: item.id,
    })) || []
  )
})

// 计算属性
const selectedExamTypeText = computed(() => selectedExamType.value)
const selectedRegionText = computed(() => selectedRegion.value)

const isInAPP = computed(() => isDeviceAppClientCookie())
// const isInAPP = ref(true)

// 方法
const onPageClick = () => {
  // 点击页面其他区域关闭弹窗
}

// 获取全局配置
const getConfig = async () => {
  try {
    const res = await getConfigRequest()
    if (res?.data) {
      globalConfig.value = res.data
      // 设置考试类型选项
      if (res.data.examtype_list) {
        examTypeOptions.value = res.data.examtype_list.map((item) => ({
          text: item.type_name,
          value: item.id,
        }))
      }
      // 设置地区选项 - 如果有省份数据的话
      if (res.data.province_list) {
        regionOptions.value = res.data.province_list.map((item) => ({
          text: item.name,
          value: item.key,
        }))
      }
    }
  } catch (error) {
    console.error("获取配置失败:", error)
  }
}

const getRegionTree = async () => {
  try {
    const res = await getRegionTreeRequest({ is_ignore: 0 })
    if (res?.data) {
      regionOptions.value = res.data.map((item) => ({
        text: item.area_name,
        value: item.id,
      }))
    }
  } catch (error) {
    console.error("获取地区树失败:", error)
  }
}

// 获取考试项目列表
const getProjectList = async () => {
  try {
    const param = {
      province: selectedRegionKey.value,
      exam_type: selectedExamTypeKey.value,
      source: "apply",
    }
    const res = await getProjectListRequest(param)
    if (res?.data) {
      projectList.value = res.data

      // 更新具体考试选项
      specificExamOptions.value = res.data.map((item) => ({
        text: item.name,
        value: item.id,
      }))

      // 优先使用路由中的project_id
      const targetProjectId = projectId.value || routeProjectId.value
      let selectedProject = null

      if (targetProjectId) {
        // 路由中有project_id，查找对应的项目
        selectedProject = res.data.find((item) => item.id == targetProjectId)
        if (!selectedProject) {
          console.warn(`路由中指定的project_id(${targetProjectId})未找到，使用默认第一个项目`)
          selectedProject = res.data[0]
        }
      } else {
        // 路由中没有project_id，使用第一个项目
        selectedProject = res.data[0]
      }

      if (selectedProject) {
        projectId.value = selectedProject.id
        projectName.value = selectedProject.name
        await getOverView()
      }
    }
  } catch (error) {
    console.error("获取项目列表失败:", error)
  }
}

const getArticleList = async () => {
  try {
    const param = {
      project_id: projectId.value,
      source: "apply",
    }
    const res = await getArticleListRequest(param)
    if (res?.data) {
      articleData.value = res.data
      let arr = res?.data?.article_list || []
      // 设置tab列表
      if (arr.length) {
        tabList.value = arr.map((item) => ({
          name: item.title,
          id: item.id,
        }))
      }

      // 优先使用路由中的article_id
      const targetArticleId = routeArticleId.value
      let selectedArticleIndex = 0
      let selectedArticle = null

      if (articleData.value.region_province) {
        let data = regionOptions.value.find(
          (item) => item.value == articleData.value.region_province
        )
        selectedRegion.value = data.text
        selectedRegionKey.value = data.value
      }

      if (articleData.value.exam_type) {
        let data = examTypeOptions.value.find((item) => item.value == articleData.value.exam_type)
        selectedExamType.value = data.text
        selectedExamTypeKey.value = data.value
      }

      if (targetArticleId) {
        // 路由中有article_id，查找对应的文章
        selectedArticleIndex = arr.findIndex((item) => item.id == targetArticleId)
        if (selectedArticleIndex === -1) {
          console.warn(`路由中指定的article_id(${targetArticleId})未找到，使用默认第一个文章`)
          selectedArticleIndex = 0
          selectedArticle = arr[0]
        } else {
          selectedArticle = arr[selectedArticleIndex]
        }
      } else {
        // 路由中没有article_id，使用第一个文章
        selectedArticleIndex = 0
        selectedArticle = arr[0]
      }

      if (selectedArticle) {
        articleId.value = selectedArticle.id
        currentTab.value = selectedArticleIndex
        await getInfo()
      }
    }
  } catch (error) {
    console.error("获取文章列表失败:", error)
  }
}

const getInfo = async () => {
  try {
    // 清空相关数据
    regionDataCache.value = {}
    jobData.count = 0
    jobData.list = []
    positionInput.value = ""
    selectedSearchUnitKey.value = ""
    selectedSearchRegionText.value = "全部地区"
    sortIndex.value = 0
    workList.value = []
    currentTableData.value = []
    displayCount.value = 10
    showLoadMore.value = true
    pageParams.page = 1
    pageParams.hasMore = true
    pageParams.loading = false
    hasSearched.value = false

    // 重置地区选择器状态
    positionRegionSelector.selectedValues = []
    positionRegionSelector.displayText = "全部地区"
    positionRegionColumns.value = []

    const param = {
      project_id: projectId.value,
      article_id: articleId.value,
    }
    const res = await getApplyDetailRequest(param)
    if (res?.data) {
      detailData.value = res.data
      updateTime.value = res.data.update_time || new Date().toLocaleString()

      // 设置地区信息
      if (detailData.value.region_num_list?.length) {
        if (detailData.value.region_num_list.length > 1) {
          const obj = {
            area_name: "全部",
            id: "",
          }
          detailData.value.region_num_list.unshift(obj)
        }
        regionName.value = detailData.value.region_num_list[0].area_name
        regionId.value = detailData.value.region_num_list[0].id
      }
      // 设置地区选择器状态
      await setupPositionRegionSelector(res.data)

      if (detailData.value.share_info) {
        const mpParam = {
          mp_app_id: "wx1234567890abcdef",
          path: "/pages/index/index",
          img: "https://example.com/image.jpg",
        }
        const param = {
          title: detailData.value.share_info.title,
          desc: detailData.value.share_info.desc,
          imgUrl: detailData.value.share_info.share_image,
          link: window.location.href,
          mpPram: JSON.stringify(mpParam),
        }
        setAppMeta(param)
      }

      // 获取用人单位列表
      const workParam = {
        region_province: positionRegionSelector.selectedValues[0] || "",
        region_city: positionRegionSelector.selectedValues[1] || "",
        region_district: positionRegionSelector.selectedValues[2] || "",
        article_id: articleId.value,
      }
      // 如果地区选择器没有默认值，则根据level设置
      if (!workParam.region_province && !workParam.region_city && !workParam.region_district) {
        switch (res.data.level) {
          case 2:
            workParam.region_province = res.data.region_province
            break
          case 3:
            workParam.region_province = res.data.region_province
            workParam.region_city = res.data.region_city
            break
          case 4:
            workParam.region_province = res.data.region_province
            workParam.region_city = res.data.region_city
            workParam.region_district = res.data.region_district
            break
        }
      }
      await getWorkList(workParam)
      await getCurrentTableData()
    }
  } catch (error) {
    console.error("获取详情失败:", error)
  }
}

// 设置地区选择器状态
const setupPositionRegionSelector = async (data) => {
  const level = data.level || 1
  positionRegionSelector.level = level

  switch (level) {
    case 4:
      // level=4: 不可编辑，需要显示具体的省-市-区文案
      positionRegionSelector.editable = false

      // 使用接口获取显示文案
      if (data.region_province && data.region_city && data.region_district) {
        try {
          // 获取省份数据
          const provinces = await getRegionDataWithCache(0, 1)
          const province = provinces?.find((p) => p.value == data.region_province)

          // 获取城市数据
          const cities = await getRegionDataWithCache(data.region_province, 2)
          const city = cities?.find((c) => c.value == data.region_city)

          // 获取区县数据
          const districts = await getRegionDataWithCache(data.region_city, 3)
          const district = districts?.find((d) => d.value == data.region_district)

          if (province && city && district) {
            positionRegionSelector.displayText = `${province.text}-${city.text}-${district.text}`
          } else {
            positionRegionSelector.displayText = "全部地区"
          }

          // 设置选中值（虽然不可编辑，但保持数据一致性）
          positionRegionSelector.selectedValues = [
            data.region_province,
            data.region_city,
            data.region_district,
          ]
        } catch (error) {
          console.error("获取地区显示文案失败:", error)
          positionRegionSelector.displayText = "全部地区"
        }
      } else {
        positionRegionSelector.displayText = "全部地区"
      }
      break
    case 3:
      // level=3: 只能选择区县
      positionRegionSelector.editable = true
      positionRegionSelector.title = "请选择区县"
      // 预加载区县数据
      if (data.region_city) {
        await loadDistrictData(data.region_city)
        // 设置默认显示文本为第一个区县
        if (positionRegionColumns.value[0] && positionRegionColumns.value[0].length > 0) {
          positionRegionSelector.displayText = "全部地区"
          positionRegionSelector.selectedValues = [data.region_province, data.region_city, ""]
        } else {
          positionRegionSelector.displayText = "全部地区"
        }
      } else {
        positionRegionSelector.displayText = "全部地区"
      }
      break
    case 2:
      // level=2: 可选择市区
      positionRegionSelector.editable = true
      positionRegionSelector.title = "请选择市区"
      // 预加载市区数据
      try {
        const cities = await getRegionDataWithCache(data.region_province, 2)
        const districts = cities.length ? await getRegionDataWithCache(cities[0].value, 3) : []
        positionRegionColumns.value = [cities, districts]

        // 设置默认显示文本为第一个城市-第一个区县
        if (cities.length > 0 && districts.length > 0) {
          positionRegionSelector.displayText = "全部地区"
          positionRegionSelector.selectedValues = [data.region_province, "", ""]
        } else if (cities.length > 0) {
          positionRegionSelector.displayText = "全部地区"
          positionRegionSelector.selectedValues = [data.region_province, ""]
        } else {
          positionRegionSelector.displayText = "全部地区"
        }
      } catch (error) {
        console.error("预加载市区数据失败:", error)
        positionRegionSelector.displayText = "全部地区"
      }
      break
    case 1:
    default:
      // level=1: 完整三级联动，预加载省市区数据
      positionRegionSelector.editable = true
      positionRegionSelector.title = "请选择地区"
      // 预加载三级联动数据
      try {
        const provinces = await getRegionDataWithCache(0, 1)
        const cities = provinces.length ? await getRegionDataWithCache(provinces[0].value, 2) : []
        const districts = cities.length ? await getRegionDataWithCache(cities[0].value, 3) : []
        positionRegionColumns.value = [provinces, cities, districts]

        // 设置默认显示文本为第一个省-第一个市-第一个区
        if (provinces.length > 0 && cities.length > 0 && districts.length > 0) {
          positionRegionSelector.displayText = "全部地区"
          positionRegionSelector.selectedValues = ["", "", ""]
        } else {
          positionRegionSelector.displayText = "全部地区"
        }
      } catch (error) {
        console.error("预加载三级联动数据失败:", error)
        positionRegionSelector.displayText = "全部地区"
      }
      break
  }
}

// 加载区县数据
const loadDistrictData = async (cityId) => {
  try {
    const param = {
      article_id: articleId.value,
      level: 3,
      parent_id: cityId,
    }
    const res = await getRegionListByArticleIdRequest(param)
    if (res?.data) {
      positionRegionColumns.value = [
        res.data.map((item) => ({
          text: item.area_name,
          value: item.id,
        })),
      ]
    }
  } catch (error) {
    console.error("获取区县数据失败:", error)
  }
}

// 根据地区获取用人单位
const getWorkList = async (param) => {
  try {
    param.article_id = articleId.value
    const res = await getWorkUnitListRequest(param)
    if (res?.data) {
      workList.value = res.data
      // 更新搜索单位选项
      searchUnitOptions.value = [
        { text: "全部单位", value: "" },
        ...res.data.map((item) => ({
          text: item.work_unit,
          value: item.work_unit,
        })),
      ]
    }
  } catch (error) {
    console.error("获取用人单位列表失败:", error)
  }
}

// 获取当前表格数据
const getCurrentTableData = async () => {
  try {
    const param = {
      region_id: regionId.value,
      project_id: projectId.value,
      article_id: articleId.value,
      type: selectedSubTab.value,
      heat: currentMainTab.value,
    }
    const res = await getStatisticsListRequest(param)
    if (res?.data) {
      const fullData = res.data || []
      const result = fullData.slice(0, displayCount.value)
      showLoadMore.value = fullData.length > displayCount.value
      currentTableData.value = result
    }
  } catch (error) {
    console.error("获取表格数据失败:", error)
  }
}

const onExamTypeClick = () => {
  selectedExamValues.value = [selectedExamTypeKey.value]
  examTypeVisible.value = true
}

const onRegionClick = () => {
  selectedRegionValues.value = [selectedRegionKey.value]
  regionVisible.value = true
}

const onSpecificExamClick = () => {
  specificExamVisible.value = true
}

const onExamTypeConfirm = ({ selectedOptions }) => {
  if (selectedOptions && selectedOptions.length > 0) {
    selectedExamType.value = selectedOptions[0].text
    selectedExamTypeKey.value = selectedExamValues.value[0]
    examTypeVisible.value = false
    projectId.value = null
    projectName.value = ""
    getProjectList()
  }
}

const onRegionConfirm = ({ selectedOptions }) => {
  if (selectedOptions && selectedOptions.length > 0) {
    selectedRegion.value = selectedOptions[0].text
    selectedRegionKey.value = selectedRegionValues.value[0]
    projectId.value = null
    projectName.value = ""
    getProjectList()
  }
  regionVisible.value = false
}

const onSpecificExamConfirm = ({ selectedOptions }) => {
  if (selectedOptions && selectedOptions.length > 0) {
    selectedSpecificExam.value = selectedOptions[0].text
    projectId.value = selectedOptions[0].value
    projectName.value = selectedOptions[0].text
    specificExamVisible.value = false
    getArticleList()
  }
}

const onTabClick = (index) => {
  if (!tabContainerRef.value || !contentContainerRef.value) return

  const currentScrollTop =
    window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop
  scrollPositions.value[currentTab.value] = currentScrollTop

  currentTab.value = index
  if (articleList.value[index]) {
    articleId.value = articleList.value[index].id

    // 先执行滚动定位（如果需要的话）
    const goalTop = scrollPositions.value[index] || 0
    let pageScrollTop
    console.log(stickyThreshold.value, "---------------------")
    console.log(currentScrollTop, "======================")

    if (currentScrollTop >= stickyThreshold.value) {
      // 已经吸顶，需要执行滚动操作
      if (goalTop < stickyThreshold.value) {
        pageScrollTop = stickyThreshold.value
      } else {
        pageScrollTop = goalTop
      }

      window.scrollTo({
        top: pageScrollTop,
        behavior: "auto",
      })
    }

    // 然后更新数据，直接替换而不是先清空
    getOverView(true)
  }
}

const onMainTabClick = (tabType) => {
  currentMainTab.value = tabType
  displayCount.value = 10
  getCurrentTableData()
}

const onSubTabClick = (subType) => {
  selectedSubTab.value = subType
  displayCount.value = 10
  getCurrentTableData()
}

const onLoadMoreClick = () => {
  displayCount.value += 10
  getCurrentTableData()
}

// 自定义图表 tooltip 格式化函数
const customTooltipFormatter = (params) => {
  const data = detailData.value.region_num_list.slice(1)[params.dataIndex]
  const text = getMainTypeText(detailData.value.show_filed)
  return [
    `<div style="font-size: 0.32rem; color: #3C3D42; margin-bottom: 8px;">${data.area_name}</div>`,
    `<div style="font-size: 0.32rem; color: #919499;"><span style="width: 1.2rem;margin-right: 0.2rem;">职位数</span> <span style="color: #3C3D42; font-weight: 400;">${data.job_num || 0}</span></div>`,
    `<div style="font-size: 0.29rem; color: #919499;"><span style="width: 1.2rem;margin-right: 0.2rem;">${text}人数</span><span style="color: #3C3D42; font-weight: 400;">${data.num || 0}</span></div>`,
    `<div style="font-size: 0.29rem; color: #919499;"><span style="width: 1.2rem;margin-right: 0.2rem;">招录人数</span> <span style="color: #3C3D42; font-weight: 400;">${data.need_num || 0}</span></div>`,
    `<div style="font-size: 0.29rem; color: #919499;"><span style="width: 1.2rem;margin-right: 0.2rem;">竞争比</span> <span style="color: #3C3D42; font-weight: 400;">${data.competitive_rate || "0:1"}</span></div>`,
  ].join("")
}

const goDetail = () => {
  // 跳转到详情页
}

// 岗位报考咨询相关方法
const onSearchRegionConfirm = ({ selectedOptions }) => {
  if (selectedOptions && selectedOptions.length > 0) {
    selectedSearchRegionText.value = selectedOptions[0].text
    showSearchRegionPopup.value = false
  }
}

// 地区选择确认
const onPositionRegionConfirm = ({ selectedOptions }) => {
  // 根据level处理不同的确认逻辑
  const level = positionRegionSelector.level

  if (level === 3 && selectedOptions.length > 0) {
    // 只选择区县
    const district = selectedOptions[0]
    positionRegionSelector.displayText = district.text
    positionRegionSelector.selectedValues = [
      detailData.value.region_province,
      detailData.value.region_city,
      district.value,
    ]
  } else if (level === 2 && selectedOptions.length > 1) {
    // 选择市区
    const city = selectedOptions[0]
    const district = selectedOptions[1]
    positionRegionSelector.displayText = `${city.text}-${district.text}`
    positionRegionSelector.selectedValues = [
      detailData.value.region_province,
      city.value,
      district.value,
    ]
  } else if (level === 1 && selectedOptions.length > 2) {
    // 三级联动
    const province = selectedOptions[0]
    const city = selectedOptions[1]
    const district = selectedOptions[2]
    positionRegionSelector.displayText = `${province.text}-${city.text}-${district.text}`
    positionRegionSelector.selectedValues = [province.value, city.value, district.value]
  }

  showSearchRegionPopup.value = false

  // 重新获取用人单位列表
  const workParam = {
    region_province: positionRegionSelector.selectedValues[0] || "",
    region_city: positionRegionSelector.selectedValues[1] || "",
    region_district: positionRegionSelector.selectedValues[2] || "",
    article_id: articleId.value,
  }
  getWorkList(workParam)
}

const onSearchUnitConfirm = ({ selectedOptions }) => {
  if (selectedOptions && selectedOptions.length > 0) {
    selectedSearchUnitText.value = selectedOptions[0].text
    selectedSearchUnitKey.value = selectedOptions[0].value
    showSearchUnitPopup.value = false
  }
}

const onSearchClick = async () => {
  try {
    pageParams.page = 1
    pageParams.hasMore = true
    pageParams.loading = false
    hasSearched.value = true

    await getJobList(false)
  } catch (error) {
    console.error("搜索失败:", error)
  }
}

const onClearSearch = () => {
  selectedSearchRegionText.value = "全部地区"
  selectedSearchUnitText.value = "全部单位"
  selectedSearchUnitKey.value = ""
  positionInput.value = ""
  sortIndex.value = 0
  sortOrder.value = ""
  // 重置地区选择器
  positionRegionSelector.selectedValues = []
  positionRegionSelector.displayText = "全部地区"
  jobData.count = 0
  jobData.list = []
}

// 数据说明展开/收起
const toggleDataDesc = () => {
  dataDescExpanded.value = !dataDescExpanded.value
}

const brandStore = useBrandStore()
const brandColor = computed(() => {
  return brandStore.color
})

const changeSortOrder = () => {
  if (sortOrder.value === "") {
    sortOrder.value = "asc"
    sortIndex.value = 1
  } else if (sortOrder.value === "asc") {
    sortOrder.value = "desc"
    sortIndex.value = 2
  } else {
    sortOrder.value = ""
    sortIndex.value = 0
  }

  if (hasSearched.value) {
    onSearchClick()
  }
}

const optionsParam = ref({
  option: {
    unitKey: "Web",
    param: {
      url: window.location.href,
    },
  },
})

const goCmdUrl = () => {
  openUrlAction(detailData.value.job_selection.cmd_json)
}

const goAppUrl = (type) => {
  let option = {
    type: "openUnit",
  }
  if (type === "notice") {
    option.option = {
      unitKey: "NoticeJobNoticeDetail",
      param: {
        id: String(articleId.value),
      },
    }
  } else if (type === "focus") {
    option.option = {
      unitKey: "NoticeJobMine",
      param: {
        type: "job",
      },
    }
  }
  openUrlAction(option)
}

// 切换关注状态
const toggleFocus = async (index) => {
  let focus = jobData.list[index].is_follows == 1
  const param = {
    item_type: "job",
    item_no: [jobData.list[index].id],
    type: focus ? "unfollow" : "follow",
  }
  const res = await setFocus(param)
  if (res) {
    showToast(focus ? "已取消关注" : "关注成功")
    jobData.list[index].is_follows = focus ? 0 : 1
  }
}

const scrollToElement = (elementRef) => {
  if (elementRef.value) {
    // 计算固定元素的总高度
    const topNavHeight = (1.17 + (topHeight.value || 0)) * 37.5 // 转换为px
    const tabContainerHeight = tabList.value.length > 1 ? 1.34 * 37.5 : 0 // Tab容器高度
    const totalFixedHeight = topNavHeight + tabContainerHeight

    // 获取目标元素的位置
    const targetRect = elementRef.value.getBoundingClientRect()
    const targetTop = targetRect.top + window.pageYOffset

    // 计算滚动位置，减去固定元素高度，再额外留一些间距
    const scrollToPosition = targetTop - totalFixedHeight - 20 // 20px额外间距

    // 平滑滚动到目标位置
    window.scrollTo({
      top: Math.max(0, scrollToPosition), // 确保不小于0
      behavior: "smooth",
    })
  }
}

const turnToPage = () => {
  scrollToElement(jobSearchSectionRef)
}

const onStatsCardClick = (item) => {
  if (item.type === "job" || item.type === "competitive_rate") {
    if (item.type === "job") {
      onMainTabClick("cold")
    } else {
      onMainTabClick("hot")
    }
    scrollToElement(positionSectionRef)
  } else if (item.type === "work_unit") {
    scrollToElement(recruitingUnitRef)
  }
}

// 获取职位列表
const getJobList = async (isLoadMore = false) => {
  if (pageParams.loading) {
    return
  }

  if (isLoadMore && !pageParams.hasMore) {
    return
  }

  pageParams.loading = true

  try {
    const params = {
      project_id: projectId.value,
      article_id: articleId.value,
      order: sortIndex.value == 1 ? "asc" : "desc",
      work_unit: selectedSearchUnitKey.value,
      keywords: positionInput.value,
      region_province: positionRegionSelector.selectedValues[0] || "",
      region_city: positionRegionSelector.selectedValues[1] || "",
      region_district: positionRegionSelector.selectedValues[2] || "",
      page: pageParams.page,
      size: pageParams.size,
    }

    const res = await getJobCompetitiveRateListRequest(params)
    if (res?.data) {
      const newList = res.data.list || []
      const totalCount = res.data.count || 0

      let updatedList = []
      if (isLoadMore) {
        updatedList = [...jobData.list, ...newList]
      } else {
        updatedList = newList
      }

      const hasMore = updatedList.length < totalCount

      jobData.count = totalCount
      jobData.list = updatedList
      pageParams.hasMore = hasMore
      pageParams.page = pageParams.page + 1
      if (newList.length == 0) {
        showToast("没有找到符合的职位~")
      }
    }
  } catch (error) {
    console.error("获取职位列表失败:", error)
  } finally {
    pageParams.loading = false
  }
}

onBeforeMount(() => {
  openAppFullscreen()
  getOverView()
  getHeight()
})

// 生命周期
onMounted(() => {
  window.addEventListener("scroll", handleScroll)
  window.addEventListener("resize", checkScreenSize)
  window.getNavigationBarHeight = getNavigationBarHeight
  // 初始化屏幕尺寸检测
  checkScreenSize()
})

onUnmounted(() => {
  // 组件卸载时移除事件监听，防止内存泄漏
  window.removeEventListener("scroll", handleScroll)
  window.removeEventListener("resize", checkScreenSize)
  delete window.getNavigationBarHeight
})

const topHeight = ref(null)

const getHeight = () => {
  if (isInAPP.value) {
    topHeight.value = 1.17
  }
  getAppStatusHeight()
}

const getNavigationBarHeight = (num) => {
  topHeight.value = Number(num) / 37.5
}

// 检测屏幕宽度的方法
const checkScreenSize = () => {
  isTablet.value = window.innerWidth > 750
}

// 计算主内容区域的margin-top
const getMainContentMarginTop = () => {
  let baseMargin = isInAPP.value ? topHeight.value - 3.68 : topHeight.value - 2.78
  // 平板模式下额外减少0.8rem
  if (isTablet.value) {
    baseMargin -= 0.8
  }
  return baseMargin + "rem"
}

// 定义滚动事件的处理函数
const handleScroll = () => {
  const scrollTop =
    window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop
  topShow.value = scrollTop > 1

  if (stickyThreshold.value > 0) {
    const shouldBeSticky = scrollTop >= stickyThreshold.value - 5
    if (isTabSticky.value !== shouldBeSticky) {
      isTabSticky.value = shouldBeSticky
    }
  }
}

const getOverView = async (flag) => {
  try {
    const param = {
      project_id: projectId.value || routeProjectId.value,
      article_id: articleId.value || routeArticleId.value,
    }
    const res = await getOverViewRequest(param)
    if (res?.data) {
      detailData.value = res.data?.detail
      updateTime.value = detailData.value.last_update_time
      // 设置考试单位
      if (res.data.examtype_list && res.data.examtype_list.length) {
        examTypeOptions.value = res.data.examtype_list.map((item) => ({
          text: item.type_name,
          value: item.id,
        }))

        let data = examTypeOptions.value.find((item) => item.value == res.data.exam_type)
        selectedExamType.value = data.text
        selectedExamTypeKey.value = data.value
      }
      // 设置地区选项 - 如果有省份数据的话
      if (res.data.province_list && res.data.province_list.length) {
        regionOptions.value = res.data.province_list.map((item) => ({
          text: item.area_name,
          value: item.id,
        }))

        let data = regionOptions.value.find((item) => item.value == res.data.region_province)
        selectedRegion.value = data.text
        selectedRegionKey.value = data.value
      }

      // 设置项目信息
      if (res.data.project_list && res.data.project_list.length) {
        specificExamOptions.value = res.data.project_list.map((item) => ({
          text: item.name,
          value: item.id,
        }))
        let selectedProject = specificExamOptions.value.find(
          (item) => item.value == routeProjectId.value
        )
        console.log(selectedProject, "-------")
        if (selectedProject) {
          projectId.value = selectedProject.value
          projectName.value = selectedProject.text
        }
      }

      if (!flag) {
        // 设置文章信息
        if (res.data.article_list && res.data.article_list.length) {
          tabList.value = res.data.article_list.map((item) => ({
            name: item.title,
            id: item.id,
          }))
          articleList.value = res.data.article_list || []
          // 计算一次 stickyThreshold
          if (stickyThreshold.value === 0 && tabList.value.length > 1) {
            nextTick(() => {
              if (tabContainerRef.value) {
                const rootFontSize = parseFloat(getComputedStyle(document.documentElement).fontSize)
                const topInPixels = (1.17 + (topHeight.value || 0)) * rootFontSize
                const rect = tabContainerRef.value.getBoundingClientRect()
                stickyThreshold.value = rect.top + window.scrollY - topInPixels
              }
            })
          }
          let selectedArticle = tabList.value.find((item) => item.id == routeArticleId.value)
          let selectedArticleIndex = tabList.value.findIndex(
            (item) => item.id == routeArticleId.value
          )
          if (selectedArticle) {
            articleId.value = selectedArticle.id
            currentTab.value = selectedArticleIndex
          } else {
            articleId.value = tabList.value[0].id
            currentTab.value = 0
          }
        }
      }
      // 设置地区信息
      if (detailData.value.region_num_list?.length) {
        if (detailData.value.region_num_list.length > 1) {
          const obj = {
            area_name: "全部",
            id: "",
          }
          detailData.value.region_num_list.unshift(obj)
        }
        regionName.value = detailData.value.region_num_list[0].area_name
        regionId.value = detailData.value.region_num_list[0].id
      }
      console.timeEnd("处理时间")
      // 首次加载完成后，将loading状态设为false
      if (isInitialLoading.value) {
        isInitialLoading.value = false
      }

      // 设置地区选择器状态
      await setupPositionRegionSelector(detailData.value)

      if (detailData.value.share_info) {
        // const mpParam = {
        //   mp_app_id: "gh_d888cb96a35c",
        //   path: "/pages/course/detail/index?province=chongqing&no=c123",
        //   img: "https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/cmmon/skb/icon/share_image.png",
        // }
        const param = {
          title: detailData.value.share_info.title,
          desc: detailData.value.share_info.desc,
          imgUrl: detailData.value.share_info.share_image,
          link: window.location.href,
          // mpPram: JSON.stringify(mpParam),
        }
        setAppMeta(param)
      }

      // 获取用人单位列表
      const workParam = {
        region_province: detailData.value.region_province || "",
        region_city: detailData.value.region_city || "",
        region_district: detailData.value.region_district || "",
        article_id: articleId.value,
      }
      // 如果地区选择器没有默认值，则根据level设置
      if (!workParam.region_province && !workParam.region_city && !workParam.region_district) {
        switch (detailData.value.level) {
          case 2:
            workParam.region_province = detailData.value.region_province
            break
          case 3:
            workParam.region_province = detailData.value.region_province
            workParam.region_city = detailData.value.region_city
            break
          case 4:
            workParam.region_province = detailData.value.region_province
            workParam.region_city = detailData.value.region_city
            workParam.region_district = detailData.value.region_district
            break
        }
      }

      await getWorkList(workParam)
      await getCurrentTableData()
    }
  } catch (error) {
    console.error("获取详情失败:", error)
  } finally {
  }
}

const onPositionLocationClick = () => {
  positionLocationVisible.value = true
}

const onPositionLocationConfirm = ({ selectedOptions }) => {
  if (selectedOptions && selectedOptions.length > 0) {
    regionId.value = selectedOptions[0].value
    regionName.value = selectedOptions[0].text
    positionLocationVisible.value = false
    getCurrentTableData()
  }
}

const onPositionRegionPopupOpen = async () => {
  // 如果数据已经预加载成功，设置默认选中值并返回
  if (positionRegionColumns.value && positionRegionColumns.value.length > 0) {
    // 设置选择器的默认选中值
    nextTick(() => {
      const picker = positionRegionPickerRef.value
      if (picker && positionRegionSelector.selectedValues.length > 0) {
        const { level } = positionRegionSelector
        const selectedValues = positionRegionSelector.selectedValues

        if (level === 1 && selectedValues.length >= 3) {
          // 三级联动，设置省市区
          const provinceIndex = positionRegionColumns.value[0].findIndex(
            (item) => item.value === selectedValues[0]
          )
          const cityIndex = positionRegionColumns.value[1].findIndex(
            (item) => item.value === selectedValues[1]
          )
          const districtIndex = positionRegionColumns.value[2].findIndex(
            (item) => item.value === selectedValues[2]
          )

          if (provinceIndex >= 0) picker.setColumnIndex(0, provinceIndex)
          if (cityIndex >= 0) picker.setColumnIndex(1, cityIndex)
          if (districtIndex >= 0) picker.setColumnIndex(2, districtIndex)
        } else if (level === 2 && selectedValues.length >= 2) {
          // 市区二级，设置市区
          const cityIndex = positionRegionColumns.value[0].findIndex(
            (item) => item.value === selectedValues[1]
          )
          const districtIndex = positionRegionColumns.value[1].findIndex(
            (item) => item.value === selectedValues[2]
          )

          if (cityIndex >= 0) picker.setColumnIndex(0, cityIndex)
          if (districtIndex >= 0) picker.setColumnIndex(1, districtIndex)
        } else if (level === 3 && selectedValues.length >= 3) {
          // 只选区县
          const districtIndex = positionRegionColumns.value[0].findIndex(
            (item) => item.value === selectedValues[2]
          )
          if (districtIndex >= 0) picker.setColumnIndex(0, districtIndex)
        }
      }
    })
    return
  }

  // 数据未预加载或预加载失败，重新加载
  const { level } = positionRegionSelector
  let provinces, cities, districts

  try {
    if (level === 1) {
      provinces = await getRegionDataWithCache(0, 1)
      cities = provinces.length ? await getRegionDataWithCache(provinces[0].value, 2) : []
      districts = cities.length ? await getRegionDataWithCache(cities[0].value, 3) : []
      positionRegionColumns.value = [provinces, cities, districts]
    } else if (level === 2) {
      cities = await getRegionDataWithCache(detailData.value.region_province, 2)
      districts = cities.length ? await getRegionDataWithCache(cities[0].value, 3) : []
      positionRegionColumns.value = [cities, districts]
    } else if (level === 3) {
      districts = await getRegionDataWithCache(detailData.value.region_city, 3)
      positionRegionColumns.value = [districts]
    }
  } catch (error) {
    console.error("打开地区选择器失败:", error)
  }
}

const onPositionRegionPickerChange = async (selectedValues, columnIndex) => {
  const picker = positionRegionPickerRef.value
  if (!picker) return

  const { level } = positionRegionSelector
  try {
    if (level === 1) {
      if (columnIndex === 0) {
        const cities = await getRegionDataWithCache(selectedValues[0], 2)
        const districts = cities.length ? await getRegionDataWithCache(cities[0].value, 3) : []
        picker.setColumnValues(1, cities)
        picker.setColumnValues(2, districts)
      } else if (columnIndex === 1) {
        const districts = await getRegionDataWithCache(selectedValues[1], 3)
        picker.setColumnValues(2, districts)
      }
    } else if (level === 2) {
      if (columnIndex === 0) {
        const districts = await getRegionDataWithCache(selectedValues[0], 3)
        picker.setColumnValues(1, districts)
      }
    }
  } catch (error) {
    console.error("地区选择器联动失败:", error)
  }
}

const getRegionDataWithCache = async (parentId, dataLevel = null) => {
  if (regionDataCache.value[parentId]) {
    return regionDataCache.value[parentId]
  }
  const data = await getRegionData(parentId, dataLevel)
  regionDataCache.value[parentId] = data
  return data
}

const getRegionData = async (parentId, dataLevel) => {
  const param = {
    article_id: articleId.value,
    level: dataLevel,
    parent_id: parentId,
  }
  try {
    const res = await getRegionListByArticleIdRequest(param)
    return (
      res?.data.map((item) => ({
        text: item.area_name || item.name,
        value: item.id,
      })) || []
    )
  } catch (error) {
    console.error("获取地区数据失败:", error)
    return []
  }
}

const onPositionRegionClick = () => {
  if (positionRegionSelector.editable) {
    showSearchRegionPopup.value = true
  }
  // level=4时不可编辑，无需响应点击事件
}

const topShow = ref(false)

//返回
const backPage = () => {
  closeAppWebview()
}
// 分享
const sharePage = () => {
  if (isInAPP.value) {
    openAppSharePopup()
  } else {
    setShare({
      title: detailData.value.share_info.title,
      desc: detailData.value.share_info.desc,
      imgUrl: detailData.value.share_info.img,
    })
  }
}

const openJob = (job) => {
  if (isInAPP.value) {
    const option = {
      type: "openUnit",
      option: {
        unitKey: "NoticeJobJobDetail",
        param: {
          id: String(job.id),
        },
      },
    }
    openUrlAction(option)
  }
}

const goJobDetail = (id) => {
  if (isInAPP.value) {
    const option = {
      type: "openUnit",
      option: {
        unitKey: "NoticeJobJobDetail",
        param: {
          id: String(id),
        },
      },
    }
    openUrlAction(option)
  } else {
    window.location.href = `https://share.jbcgk.com/position/job/detail?id=${id}`
  }
}

const formattedNumber = computed(() => {
  return number.value.toLocaleString()
})
</script>

<style lang="scss" scoped>
.big-data {
  min-height: 100vh;
  position: relative;
  background: #f7f8fa;
  touch-action: pan-y; // 只允许垂直滑动，禁用双击缩放
  -webkit-user-select: none; // 禁用文本选择
  user-select: none;

  // 平板适配：移除最大宽度限制
  @media (min-width: 751px) {
    :global(html) {
      max-width: none !important;
    }
  }

  .top-bg {
    width: 100%;
    position: relative;
    z-index: 0;

    .bg-img {
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
      z-index: -1;
    }

    .top-area {
      padding: 0.21rem 0.533rem 0.533rem; // 104rpx 40rpx 40rpx = 1.387rem 0.533rem 0.533rem
      display: flex;
      flex-direction: column;
      align-items: center;
      position: relative;
      z-index: 1;

      // 平板布局的选择器样式
      .select-tablet {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 0.35rem;
        margin-top: 0.853rem; // 64rpx = 64/75 = 0.853rem
        margin-bottom: 0.347rem; // 26rpx = 26/75 = 0.347rem

        .select-item {
          flex: 1;
          display: flex;
          align-items: center;
          justify-content: space-between;
          background: rgba(255, 255, 255, 0.7);
          backdrop-filter: blur(0.853rem); // 64rpx = 64/75 = 0.853rem
          border-radius: 0.16rem; // 12rpx = 12/75 = 0.16rem
          border: 0.013rem solid rgba(255, 255, 255, 0.4); // 1rpx = 1/75 = 0.013rem
          padding: 0.24rem 0.267rem 0.24rem 0.32rem; // 18rpx 20rpx 18rpx 24rpx = 0.24rem 0.267rem 0.24rem 0.32rem
          box-sizing: border-box;

          .text {
            color: #22242e;
            font-size: 0.347rem; // 26rpx = 26/75 = 0.347rem
          }

          .arrow {
            width: 0.427rem; // 32rpx = 32/75 = 0.427rem
            height: 0.427rem; // 32rpx = 32/75 = 0.427rem
          }
        }
      }

      .back-btn {
        position: absolute;
        top: 1.493rem; // 112rpx = 112/75 = 1.493rem
        left: 0.533rem; // 40rpx = 40/75 = 0.533rem
        width: 0.533rem; // 40rpx = 40/75 = 0.533rem
        height: 0.533rem; // 40rpx = 40/75 = 0.533rem
        z-index: 1000;
        cursor: pointer;

        .back-icon {
          width: 100%;
          height: 100%;
        }
      }

      .top-title-img {
        width: 3.73rem;
        height: 0.75rem;
      }

      .title {
        font-size: 0.32rem; // 24rpx = 24/75 = 0.32rem
        color: #ffffff;
        margin-top: 0.213rem; // 16rpx = 16/75 = 0.213rem
      }

      .select-one {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 0.853rem; // 64rpx = 64/75 = 0.853rem
        margin-bottom: 0.347rem; // 26rpx = 26/75 = 0.347rem
      }

      .select-item {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: space-between;
        background: rgba(255, 255, 255, 0.7);
        backdrop-filter: blur(0.853rem); // 64rpx = 64/75 = 0.853rem
        border-radius: 0.16rem; // 12rpx = 12/75 = 0.16rem
        border: 0.013rem solid rgba(255, 255, 255, 0.4); // 1rpx = 1/75 = 0.013rem
        padding: 0.24rem 0.267rem 0.24rem 0.32rem; // 18rpx 20rpx 18rpx 24rpx = 0.24rem 0.267rem 0.24rem 0.32rem
        box-sizing: border-box;
        &:first-of-type {
          margin-right: 0.35rem;
        }

        &.w100 {
          width: 100%;
          margin-top: 0;
        }

        .text {
          color: #22242e;
          font-size: 0.347rem; // 26rpx = 26/75 = 0.347rem
        }

        .arrow {
          width: 0.427rem; // 32rpx = 32/75 = 0.427rem
          height: 0.427rem; // 32rpx = 32/75 = 0.427rem
        }
      }
    }
  }

  .main-content {
    width: 100%;
    display: flex;
    flex-direction: column;
    position: relative;
    z-index: 200;
    padding-bottom: 1.2rem;

    // Tab导航区域样式
    .tab-container {
      position: sticky;
      left: 0;
      height: 1.34rem;
      z-index: 99999999;
      width: 100%;
      background: #ffffff;
      border-bottom: 0.027rem solid #ebecf0; // 2rpx = 2/75 = 0.027rem
      overflow: hidden;
      border-radius: 0.32rem 0.32rem 0 0; // 24rpx = 24/75 = 0.32rem

      // 平板适配：隐藏下边框
      @media (min-width: 751px) {
        border-bottom: none;
      }

      .tab-scroll {
        width: 100%;
        white-space: nowrap;
        overflow-x: auto;
        height: 1.34rem;

        // 隐藏滚动条
        &::-webkit-scrollbar {
          display: none;
        }

        -ms-overflow-style: none; // IE和Edge
        scrollbar-width: none; // Firefox

        .tab-list {
          display: flex;
          align-items: center;
          height: 100%;

          .tab-item {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            height: 100%;
            padding: 0 0.43rem;
            font-size: 0.373rem; // 28rpx = 28/75 = 0.373rem
            color: #666666;
            position: relative;
            white-space: nowrap;
            flex-shrink: 0; // 防止tab项被压缩
            cursor: pointer;

            &.active {
              color: #22242e;
              font-weight: bold;
              font-size: 0.427rem; // 32rpx = 32/75 = 0.427rem

              &::after {
                content: "";
                position: absolute;
                bottom: 0;
                left: 50%;
                transform: translateX(-50%);
                width: 1.44rem; // 108rpx = 108/75 = 1.44rem
                height: 0.08rem; // 6rpx = 6/75 = 0.08rem
                background: #e60003;
              }
            }
          }
        }
      }
    }

    .zhanwei-box {
      width: 100%;
      height: 0.21rem;
      background: #f7f8fa;
    }

    .tab-container.no-radius {
      border-radius: 0;
    }
    // 内容区域样式
    .content-container {
      flex: 1;
      width: 100%;

      .content-area {
        padding: 0.1rem 0.427rem 0.533rem 0.427rem;
        box-sizing: border-box;

        // 招聘公告标题
        .announcement-title {
          margin-bottom: 0.427rem; // 32rpx = 32/75 = 0.427rem
          cursor: pointer;

          .text {
            font-size: 0.35rem; // 26rpx = 26/75 = 0.347rem
            color: #5f7e95;
            vertical-align: middle;
            line-height: 0.53rem;
          }

          .arrow {
            display: inline-block;
            width: 0.43rem;
            height: 0.43rem;
            margin-left: 0.05rem;
            vertical-align: middle;
          }
        }

        // 统计卡片容器
        .stats-container {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 0.32rem; // 24rpx = 24/75 = 0.32rem
          margin-bottom: 0.427rem; // 32rpx = 32/75 = 0.427rem

          // 平板适配：卡片一行显示，使用flex布局
          @media (min-width: 751px) {
            display: flex;
            justify-content: space-between;
            gap: 0.427rem; // 卡片间距
          }

          .stats-card {
            height: 2.56rem; // 192rpx = 192/75 = 2.56rem
            border-radius: 0.213rem; // 16rpx = 16/75 = 0.213rem
            padding: 0.32rem 0.187rem 0.32rem 0.32rem; // 24rpx 14rpx 24rpx 24rpx = 0.32rem 0.187rem 0.32rem 0.32rem
            box-sizing: border-box;
            position: relative;
            display: flex;
            flex-direction: column;
            justify-content: center;

            // 平板适配：确保卡片等宽
            @media (min-width: 751px) {
              flex: 1; // 每个卡片占等宽空间
            }

            .card-title {
              font-size: 0.32rem; // 24rpx = 24/75 = 0.32rem
              margin-bottom: 0.213rem; // 16rpx = 16/75 = 0.213rem
              display: flex;
              align-items: center;
              justify-content: space-between;

              .arrow-icon {
                width: 0.427rem; // 32rpx = 32/75 = 0.427rem
                height: 0.427rem; // 32rpx = 32/75 = 0.427rem
              }
            }

            .card-number {
              display: flex;
              align-items: flex-end;
              margin-bottom: 0.107rem; // 8rpx = 8/75 = 0.107rem

              .number {
                font-size: 0.587rem; // 44rpx = 44/75 = 0.587rem
                font-weight: 400;
                font-family: "DINBold";
              }

              .unit {
                font-size: 0.32rem; // 24rpx = 24/75 = 0.32rem
                margin-left: 0.107rem; // 8rpx = 8/75 = 0.107rem
                margin-bottom: 0.107rem; // 8rpx = 8/75 = 0.107rem
              }

              .card-type {
                padding: 0.027rem 0.107rem; // 2rpx 8rpx = 0.027rem 0.107rem
                border-radius: 0.107rem; // 8rpx = 8/75 = 0.107rem
                font-size: 0.267rem; // 20rpx = 20/75 = 0.267rem
                margin-bottom: 0.107rem; // 8rpx = 8/75 = 0.107rem
                margin-left: 0.107rem; // 8rpx = 8/75 = 0.107rem
                white-space: nowrap; // 强制不换行
              }
            }

            .card-desc {
              margin-top: 0.107rem; // 8rpx = 8/75 = 0.107rem
              font-size: 0.293rem; // 22rpx = 22/75 = 0.293rem
            }

            // 橙色卡片 - 报名总人数
            &.orange-card {
              background: linear-gradient(315deg, #fff4f0 0%, #ffede8 100%);
              border: 0.013rem solid #ffe5de; // 1rpx = 1/75 = 0.013rem

              .card-title {
                color: rgba(186, 87, 57, 0.7);
              }

              .card-number .number {
                color: #ba5739;
              }

              .card-number .unit,
              .card-desc {
                color: rgba(186, 87, 57, 0.5);
              }
            }

            &.blue-card {
              background: linear-gradient(315deg, #f4f5ff 0%, #ebedff 100%);
              border: 0.013rem solid #e5e7ff; // 1rpx = 1/75 = 0.013rem

              .card-title {
                color: rgba(81, 89, 159, 0.7);
              }

              .card-number .number {
                color: #51599f;
              }

              .card-number .unit,
              .card-desc {
                color: rgba(81, 89, 159, 0.5);
              }

              .card-type {
                background: rgba(255, 255, 255, 0.8);
                border: 0.013rem solid rgba(81, 89, 159, 0.2); // 1rpx = 1/75 = 0.013rem
                color: rgba(81, 89, 159, 0.8);
              }
            }

            // 浅蓝色卡片 - 竞争激烈职位
            &.light-blue-card {
              background: linear-gradient(315deg, #f0f8ff 0%, #e8f0fe 100%);
              border: 0.013rem solid #dfeafd; // 1rpx = 1/75 = 0.013rem

              .card-title {
                color: rgba(71, 109, 170, 0.7);
              }

              .card-number .number {
                color: #476daa;
              }

              .card-number .unit,
              .card-desc {
                color: rgba(71, 109, 170, 0.5);
              }

              .card-type {
                background: rgba(255, 255, 255, 0.8);
                border: 0.013rem solid rgba(71, 109, 170, 0.2); // 1rpx = 1/75 = 0.013rem
                color: rgba(71, 109, 170, 0.8);
              }
            }

            &.cyan-card {
              background: linear-gradient(315deg, #f5fcff 0%, #ddf4ff 100%);
              border: 0.013rem solid #d3f1ff; // 1rpx = 1/75 = 0.013rem

              .card-title {
                color: rgba(58, 125, 159, 0.7);
              }

              .card-number .number {
                color: #51599f;
              }

              .card-type {
                background: rgba(255, 255, 255, 0.8);
                border: 0.013rem solid rgba(58, 125, 159, 0.2); // 1rpx = 1/75 = 0.013rem
                color: rgba(58, 125, 159, 0.8);
              }

              .card-number .unit,
              .card-number .desc-text,
              .card-desc {
                color: rgba(58, 125, 159, 0.5);
              }
            }
          }
        }

        // 数据说明
        .data-desc {
          .data-desc-content {
            font-size: 0.32rem; // 24rpx = 24/75 = 0.32rem
            color: #999999;
            line-height: 1.5;

            &.collapsed {
              display: flex;
              align-items: flex-start;

              .data-desc-text {
                display: -webkit-box;
                -webkit-line-clamp: 1;
                -webkit-box-orient: vertical;
                overflow: hidden;
                text-overflow: ellipsis;
                flex: 1;
                margin-right: 0.107rem; // 8rpx = 8/75 = 0.107rem
              }

              .expand-btn {
                flex-shrink: 0;
              }
            }

            .data-desc-text {
              .expand-btn.inline {
                display: inline;
                margin-left: 0.107rem; // 8rpx = 8/75 = 0.107rem
              }
            }

            .expand-btn {
              display: inline-flex;
              align-items: center;
              cursor: pointer;

              .blue-text {
                font-size: 0.32rem; // 24rpx = 24/75 = 0.32rem
                color: #448aff;
                margin-right: 0.107rem; // 8rpx = 8/75 = 0.107rem
              }

              .arrow-icon {
                width: 0.43rem; // 24rpx = 24/75 = 0.32rem
                height: 0.43rem; // 24rpx = 24/75 = 0.32rem
                transition: transform 0.3s;
                vertical-align: middle;

                &.rotated {
                  transform: rotate(180deg);
                }
              }
            }
          }
        }

        // 图表区域
        .chart-section-wrapper {
          margin-bottom: 0.213rem; // 16rpx
          .chart-section {
            padding: 0.53rem 0.43rem; // 24rpx
            background: #ffffff;
            border-radius: 0.16rem; // 12rpx

            .chart-title {
              font-size: 0.427rem;
              color: #22242e;
              font-weight: bold;
              margin-bottom: 0.32rem; // 24rpx
            }

            .chart-scroll-container {
              width: 100%;
              overflow-x: auto;

              &::-webkit-scrollbar {
                display: none;
              }

              -ms-overflow-style: none; // IE和Edge
              scrollbar-width: none; // Firefox
            }

            .chart-container {
              height: 6rem; // 进一步增加图表高度以拉开y轴间距
            }
          }
        }
      }
    }
  }

  // 图表区域
  .chart-section {
    background: #ffffff;
    padding: 0.53rem 0 0.53rem 0.43rem; // 24rpx
    margin-bottom: 0.213rem; // 16rpx

    .chart-title {
      font-size: 0.427rem;
      color: #22242e;
      font-weight: bold;
      margin-bottom: 0.32rem; // 24rpx
    }

    .chart-scroll-container {
      width: 100%;
      overflow-x: auto;

      &::-webkit-scrollbar {
        display: none;
      }

      -ms-overflow-style: none; // IE和Edge
      scrollbar-width: none; // Firefox
    }

    .chart-container {
      height: 5rem; // 增加图表高度以拉开y轴间距
    }
  }

  // 职位数据表格区域
  .position-section {
    background: #ffffff;
    padding: 0.32rem; // 24rpx

    .position-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 0.32rem; // 24rpx = 24/75 = 0.32rem

      .position-title {
        font-size: 0.427rem; // 32rpx = 32/75 = 0.427rem
        font-weight: bold;
        color: #22242e;
      }

      .position-location {
        display: flex;
        align-items: center;
        font-size: 0.347rem; // 26rpx = 26/75 = 0.347rem
        color: #666666;

        .location-arrow {
          width: 0.427rem; // 32rpx = 32/75 = 0.427rem
          height: 0.427rem; // 32rpx = 32/75 = 0.427rem
          margin-left: 0.107rem; // 8rpx = 8/75 = 0.107rem
        }
      }
    }

    .position-tab-container {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 0.32rem; // 24rpx = 24/75 = 0.32rem

      .position-tab-list {
        display: flex;
        align-items: center;
        border-radius: 0.107rem; // 8rpx = 8/75 = 0.107rem
        overflow: hidden;
        width: fit-content;

        .position-tab-item {
          padding: 0.133rem 0.267rem; // 10rpx 20rpx = 0.133rem 0.267rem
          margin-right: 0;
          font-size: 0.32rem; // 24rpx = 24/75 = 0.32rem
          color: #666666;
          border: 0.027rem solid #ebecf0; // 2rpx = 2/75 = 0.027rem
          border-radius: 0;
          background: #f7f8fa;
          cursor: pointer;

          &.active {
            color: #ffffff;
            background: #e60003;
            border: 0.027rem solid transparent; // 2rpx = 2/75 = 0.027rem
          }
        }
      }

      .position-sub-tab-list {
        display: flex;
        align-items: center;
        gap: 0.213rem; // 16rpx = 16/75 = 0.213rem
        width: fit-content;

        .position-sub-tab-item {
          padding: 0.133rem 0.373rem; // 10rpx 28rpx = 0.133rem 0.373rem
          margin-left: 0;
          font-size: 0.32rem; // 24rpx = 24/75 = 0.32rem
          color: #3c3d42;
          background: #ffffff;
          border: 0.013rem solid #ebecf0; // 1rpx = 1/75 = 0.013rem
          border-radius: 0.107rem; // 8rpx = 8/75 = 0.107rem
          font-weight: 400;
          cursor: pointer;

          &.active {
            color: rgba(230, 0, 3, 0.8);
            background: rgba(230, 0, 3, 0.05);
            border: 0.013rem solid rgba(230, 0, 3, 0.3); // 1rpx = 1/75 = 0.013rem
          }
        }
      }
    }

    .position-table-content {
      .position-table-header {
        display: flex;
        align-items: center;
        background: linear-gradient(180deg, #fef2f2 0%, rgba(254, 242, 242, 0.5) 100%);
        padding: 0.267rem 0 0.24rem 0; // 20rpx 0 18rpx 0 = 0.267rem 0 0.24rem 0
        border-radius: 0.107rem; // 8rpx = 8/75 = 0.107rem

        .header-item {
          font-size: 0.32rem; // 24rpx = 24/75 = 0.32rem
          color: #93292c;
          text-align: center;
          display: flex;
          flex-direction: column;
          align-items: flex-start;
          justify-content: center;
          margin: 0 0.107rem; // 8rpx = 8/75 = 0.107rem

          &.header-rank {
            width: 1.067rem; // 80rpx
            justify-content: center;
            align-items: center;
          }

          &.header-position {
            flex: 1;
            align-items: flex-start;
            padding-left: 0.107rem; // 8rpx
          }

          &.header-unit {
            width: 1.867rem; // 140rpx
            align-items: flex-start;
            padding-left: 0.107rem; // 8rpx
          }

          &.header-recruit,
          &.header-apply {
            width: 1.6rem; // 120rpx
            justify-content: flex-start;
            align-items: flex-start;
          }

          span {
            line-height: 1.2;
            margin: 0.027rem 0; // 2rpx = 2/75 = 0.027rem
          }
        }
      }

      .position-table-body {
        .position-table-row {
          display: flex;
          align-items: center;
          padding: 0.16rem 0; // 12rpx = 12/75 = 0.16rem
          border-bottom: 0.013rem solid #f0f0f0; // 1rpx = 1/75 = 0.013rem

          .table-item {
            font-size: 0.32rem; // 24rpx = 24/75 = 0.32rem
            color: #22242e;
            text-align: left;
            display: flex;
            align-items: center;
            justify-content: flex-start;
            margin: 0 0.107rem; // 8rpx = 8/75 = 0.107rem

            &.table-rank {
              width: 1.067rem; // 80rpx
              margin: 0;
              justify-content: center;
            }

            &.table-position {
              flex: 1;
              padding-left: 0.107rem; // 8rpx

              .position-title {
                font-size: 0.293rem; // 22rpx = 22/75 = 0.293rem
                color: #448aff;
                line-height: 1.3;
                text-align: left; // 确保左对齐
              }
            }

            &.table-unit {
              width: 1.867rem; // 140rpx
              padding-left: 0.107rem; // 8rpx

              .unit-title {
                font-size: 0.293rem; // 22rpx = 22/75 = 0.293rem
                color: #666666;
                line-height: 1.3;
                text-align: left; // 确保左对齐
              }
            }

            &.table-recruit {
              width: 1.6rem; // 120rpx
              justify-content: flex-start;
              text-align: left;

              .recruit-num {
                font-size: 0.32rem; // 24rpx = 24/75 = 0.32rem
                color: #333333;
                font-weight: bold;
              }
            }

            &.table-apply {
              width: 1.6rem; // 120rpx
              justify-content: flex-start;
              text-align: left;

              .apply-num {
                font-size: 0.32rem; // 24rpx = 24/75 = 0.32rem
                color: #333333;
                font-weight: bold;
              }
            }
          }
        }
      }

      .load-more-section {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0.213rem 0; // 16rpx = 16/75 = 0.213rem
        cursor: pointer;

        .load-more-text {
          font-size: 0.347rem; // 26rpx = 26/75 = 0.347rem
          color: #666666;
          margin-right: 0.107rem; // 8rpx = 8/75 = 0.107rem
        }

        .load-more-icon {
          width: 0.43rem;
          height: 0.43rem;
        }
      }
    }
  }

  // 招考单位TOP10
  .recruiting-unit {
    background: #ffffff;
    padding: 0.32rem; // 24rpx

    .recruit-title {
      font-size: 0.427rem; // 32rpx = 32/75 = 0.427rem
      font-weight: bold;
      color: #22242e;
      margin-bottom: 0.213rem; // 16rpx = 16/75 = 0.213rem
    }

    .position-table-content-2 {
      .position-table-header {
        display: flex;
        align-items: center;
        background: linear-gradient(180deg, #fef2f2 0%, rgba(254, 242, 242, 0.5) 100%);
        padding: 0.267rem 0 0.24rem 0; // 20rpx 0 18rpx 0 = 0.267rem 0 0.24rem 0
        border-radius: 0.107rem; // 8rpx = 8/75 = 0.107rem

        .header-item {
          font-size: 0.32rem; // 24rpx = 24/75 = 0.32rem
          color: #93292c;
          text-align: center;
          display: flex;
          flex-direction: column;
          align-items: flex-start;
          justify-content: center;
          margin: 0 0.107rem; // 8rpx = 8/75 = 0.107rem

          &.header-rank {
            width: 0.8rem; // 60rpx
            justify-content: center;
            align-items: center;
          }

          &.header-position {
            flex: 1;
            align-items: flex-start;
            padding-left: 0.107rem; // 8rpx
          }

          &.header-unit,
          &.header-recruit,
          &.header-apply,
          &.header-competition {
            width: 1.2rem; // 90rpx
            justify-content: flex-start;
            align-items: center;
          }

          span {
            line-height: 1.2;
            margin: 0.027rem 0; // 2rpx = 2/75 = 0.027rem
          }
        }
      }

      .position-table-body {
        .position-table-row {
          display: flex;
          align-items: center;
          padding: 0.213rem 0; // 16rpx = 16/75 = 0.213rem
          border-bottom: 0.027rem solid #f0f0f0; // 2rpx = 2/75 = 0.027rem

          .table-item {
            font-size: 0.32rem; // 24rpx = 24/75 = 0.32rem
            color: #22242e;
            text-align: left;
            display: flex;
            align-items: center;
            justify-content: flex-start;
            margin: 0 0.107rem; // 8rpx = 8/75 = 0.107rem

            &.table-rank {
              width: 0.8rem; // 60rpx
              justify-content: center;
            }

            &.table-position {
              flex: 1;
              padding-left: 0.107rem; // 8rpx

              .position-title {
                font-size: 0.293rem; // 22rpx
                color: #333333;
                line-height: 1.3;
                text-align: left;
              }
            }

            &.table-unit,
            &.table-recruit,
            &.table-apply,
            &.table-competition {
              width: 1.2rem; // 90rpx
              justify-content: flex-start;
              text-align: left;

              .unit-title,
              .recruit-num,
              .apply-num,
              .competition-rate {
                font-size: 0.32rem; // 24rpx = 24/75 = 0.32rem
                color: #333333;
              }
            }
          }
        }
      }
    }
  }
}

// Vant组件样式覆盖
:deep(.van-popup) {
  z-index: 10000 !important;
}

:deep(.van-overlay) {
  z-index: 9999 !important;
}

:deep(.van-picker-column__item) {
  font-size: 0.373rem; // 28rpx = 28/75 = 0.373rem
  font-weight: 400;
  color: #999999;
}

:deep(.van-picker-column__item--selected) {
  color: #333333;
}

// 宣传横幅图片样式
.promo-banner-img {
  margin-top: 0.56rem; // 42rpx
  width: 100%;
  height: 2.933rem; // 220rpx
  display: block;
}

// 岗位报考咨询区域样式
.job-search-section {
  padding: 0.533rem 0.427rem; // 40rpx 32rpx
  box-sizing: border-box;
  background: #f7f8fa;

  .job-search-title {
    font-size: 0.427rem; // 32rpx
    color: #22242e;
    font-weight: bold;
    margin-bottom: 0.427rem; // 32rpx
  }

  .search-box {
    position: relative;
    width: 100%;
    border-radius: 0.32rem; // 24rpx
    padding: 0.107rem; // 8rpx
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    z-index: 1;

    .search-bg {
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
      z-index: 0;
      border-radius: 0.32rem; // 24rpx
    }

    .white-box {
      position: relative;
      z-index: 2;
      flex: 1;
      width: 100%;
      border-radius: 0.213rem; // 16rpx
      padding: 0.533rem; // 40rpx
      box-sizing: border-box;

      .select-item {
        background: #ffffff;
        border-radius: 0.213rem; // 16rpx
        border: 0.013rem solid #ebecf0; // 1rpx
        margin-bottom: 0.32rem; // 24rpx
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.347rem 0.32rem 0.347rem 0.427rem; // 26rpx 24rpx 26rpx 32rpx
        box-sizing: border-box;

        &:last-of-type {
          margin-bottom: 0;
        }

        // 不可编辑状态样式
        &.disabled {
          background: #f5f5f5 !important;
          border-color: #e0e0e0 !important;
          opacity: 0.6;
          pointer-events: none;

          .select-left {
            color: #999999 !important;

            .label {
              color: #999999 !important;
            }

            .selected-text {
              color: #999999 !important;
            }
          }

          .arrow-icon {
            opacity: 0.5;
          }
        }

        .select-left {
          display: flex;
          align-items: center;
          color: #3c3d42;
          font-size: 0.347rem; // 26rpx
          flex: 1;

          .label {
            width: 1.85rem; // 130rpx
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-shrink: 0; // 防止标签被压缩
          }

          .selected-text {
            flex: 1;
            margin-left: 0; // 紧贴标签
          }
        }

        .arrow-icon {
          width: 0.43rem; // 32rpx
          height: 0.43rem; // 32rpx
        }

        // 输入框样式
        &.input-item {
          .select-left {
            .position-input {
              flex: 1;
              font-size: 0.347rem; // 26rpx
              color: #3c3d42;
              background: transparent;
              border: none;
              outline: none;
              padding: 0;
              margin-left: 0; // 紧贴标签

              &::placeholder {
                color: #919499;
                font-size: 0.347rem; // 26rpx
              }
            }
          }
        }
      }

      .search-btn {
        margin-top: 0.21rem; // 40rpx
        width: 100%;
        height: 1.12rem; // 84rpx
        border-radius: 0.213rem; // 16rpx
        font-size: 0.373rem; // 28rpx
        font-weight: 500;

        :deep(.van-icon) {
          margin-right: 0.107rem; // 8rpx
        }
      }

      .clear-btn {
        width: 100%;
        text-align: center;
        margin-top: 0.427rem; // 32rpx
        color: #ec3e33;
        font-size: 0.373rem; // 28rpx
        font-weight: 500;
        cursor: pointer;
      }
    }
  }
}

// 结果统计区域样式
.result-area {
  padding: 0 0.427rem 0.533rem 0.427rem; // 40rpx 32rpx
  box-sizing: border-box;
  background: #f7f8fa;

  .result-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 0.427rem;

    .result-left {
      display: flex;
      align-items: center;

      .result-text {
        font-size: 0.32rem; // 24rpx
        color: #3c3d42;
        margin-right: 0.533rem; // 40rpx

        .result-number {
          font-weight: 500;
          color: #ec3e33;
        }
      }

      .sort-btn {
        padding: 0.16rem 0.213rem 0.16rem 0.32rem; // 12rpx 16rpx 12rpx 24rpx
        box-sizing: border-box;
        display: flex;
        align-items: center;
        background: #ffffff;
        border: 0.027rem solid #ebecf0; // 2rpx
        border-radius: 0.16rem; // 12rpx
        cursor: pointer;

        span {
          color: #3c3d42;
          font-size: 0.32rem; // 24rpx
          margin-right: 0.107rem; // 8rpx
        }

        .sort-icon {
          width: 0.427rem; // 32rpx
          height: 0.427rem; // 32rpx
        }
      }
    }

    .result-right {
      display: flex;
      align-items: center;
      cursor: pointer;

      .focus-text {
        font-size: 0.32rem; // 24rpx
        color: #448aff;
        margin-right: 0.107rem; // 8rpx
      }

      .arrow-icon {
        width: 0.427rem; // 32rpx
        height: 0.427rem; // 32rpx
      }
    }
  }
}

// 底部固定导航栏样式
.action-bar-box {
  display: flex;
  height: 1.893rem; // 142rpx × 0.5 = 71px, 71px ÷ 37.5 = 1.893rem
  z-index: 998;
  position: relative;
  box-sizing: border-box;

  .bottom-fixed-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: #ffffff;
    height: 1.893rem;
    padding: 0.187rem 0.43rem; // 14rpx 32rpx 14rpx 42rpx
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-top: 0.013rem solid #f0f0f0; // 1rpx
    z-index: 999;

    .nav-left {
      min-width: 3.2rem;
      display: flex;
      align-items: center;

      .nav-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-right: 0.533rem; // 40rpx
        cursor: pointer;

        .nav-icon {
          width: 0.533rem; // 40rpx
          height: 0.533rem; // 40rpx
          margin-bottom: 0.053rem; // 4rpx
        }

        span {
          color: #919499;
          font-size: 0.267rem; // 20rpx
        }
      }
    }

    .nav-item-area {
      flex: 1;

      .nav-btn {
        width: 100%;
        border-radius: 0.213rem; // 16rpx
        font-size: 0.373rem; // 28rpx
        font-weight: 500;
        height: 1.12rem;
        color: #ffffff;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #e60003;
      }
    }
  }
}

// 职位卡片列表样式
.job-card-list {
  // 平板适配：职位卡片一行显示两个
  @media (min-width: 751px) {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.427rem; // 32rpx
  }

  .job-card {
    background: #ffffff;
    border-radius: 0.16rem; // 12rpx
    padding: 0.427rem; // 32rpx
    margin-bottom: 0.213rem; // 16rpx

    // 平板适配：调整卡片间距
    @media (min-width: 751px) {
      margin-bottom: 0; // 移除底部间距，由grid gap控制
    }

    .job-card-top {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 0.32rem; // 24rpx

      .job-name {
        font-size: 0.427rem; // 32rpx
        font-weight: bold;
        color: #22242e;
        flex: 1;
      }

      .focus-btn {
        border-radius: 0.107rem; // 8rpx
        border: 0.027rem solid #ebecf0; // 2rpx
        background: #ffffff;
        width: 1.813rem; // 136rpx
        height: 0.64rem; // 48rpx
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;

        &:not(.focused) {
          border: 0.027rem solid rgba(230, 0, 3, 0.5); // 2rpx

          .focus-text {
            color: #e60003;
          }
        }

        &.focused {
          .focus-text {
            color: #919499;
          }
        }

        .focus-icon {
          width: 0.32rem; // 24rpx
          height: 0.32rem; // 24rpx
          margin-right: 0.107rem; // 8rpx
        }

        .focus-text {
          font-size: 0.32rem; // 24rpx
        }
      }
    }

    .job-data-area {
      display: flex;
      align-items: flex-start;
      flex-wrap: wrap;

      .data-item {
        display: flex;
        align-items: center;
        margin-bottom: 0.213rem; // 16rpx
        box-sizing: border-box;

        // 左侧占60%，右侧占40%
        &:nth-child(odd) {
          width: 60%;
          padding-right: 0.213rem; // 16rpx 右边距
        }

        &:nth-child(even) {
          width: 40%;
        }

        &.mb0 {
          margin-bottom: 0;
        }

        &.w100 {
          width: 100% !important;
        }

        .data-title {
          flex-shrink: 0;
          text-align: left;
          font-size: 0.32rem; // 24rpx
          color: #919499;
          margin-right: 0.107rem; // 8rpx
          width: 1.7rem;
        }

        .data-content {
          color: #3c3d42;
          font-weight: 400;
          font-size: 0.32rem; // 24rpx
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;

          .bold {
            font-weight: bold !important;
          }

          &.red {
            font-size: 0.32rem; // 24rpx
            color: #e60003;
            font-weight: bold;
          }
        }
      }
    }
  }
}

.rank-badge {
  display: flex;
  align-items: center;
  justify-content: center;

  &.rank-1 {
    color: #e60003;
  }

  &.rank-2 {
    color: #ff6a4d;
  }

  &.rank-3 {
    color: #ffb474;
  }
}

.w100 {
  width: 100% !important;
}

:deep(.van-popup) {
  border-radius: 0.32rem 0.32rem 0 0;
}

:deep(..van-picker__title) {
  font-size: 0.43rem;
  color: #313436;
}

:deep(.van-picker__cancel) {
  color: #c2c5cc;
  font-size: 0.37rem;
}

:deep(.van-picker__confirm) {
  color: #e60003;
  font-size: 0.37rem;
}

.top-nav {
  width: 100%;
  background: transparent;
  position: fixed;
  left: 0;
  top: 0;
  box-sizing: border-box;
  padding: 0 0.53rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  z-index: 999;

  .img {
    width: 0.53rem;
    height: 0.53rem;
  }

  .title {
    color: #22242e;
    font-size: 0.45rem;
    font-weight: bold;
  }
}

.white_bg {
  background: #ffffff;
}
.no-tab-list {
  border-radius: 0.32rem 0.32rem 0 0;
  padding-top: 0.43rem;
}

// Loading 状态样式
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;

  .loading-spinner {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.32rem;

    .spinner {
      width: 1rem;
      height: 1rem;
      border: 0.1rem solid #f3f3f3;
      border-top: 0.1rem solid #e60003;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    .loading-text {
      font-size: 0.373rem;
      color: #666666;
    }
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
